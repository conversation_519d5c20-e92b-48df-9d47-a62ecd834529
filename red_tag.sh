#!/usr/bin/env bash

# Trigger Command-C in the frontmost app (e.g., Pixea), then add <PERSON>er's "Red" tag
osascript <<'APPLESCRIPT'
-- Send Command-C to copy the path from Pixea
tell application "System Events" to keystroke "c" using command down
delay 0.25 -- give the clipboard a moment to update; increase if needed

set raw to the clipboard as text
if raw is "" then return

set lines to paragraphs of raw
repeat with L in lines
    set s to contents of L
    if s is "" then
        -- skip
    else
        -- Convert file:// URL to POSIX path if necessary
        set posixPath to s
        if s starts with "file://" then
            set posixPath to do shell script "python3 -c 'import sys,urllib.parse as u; p=u.urlsplit(sys.argv[1]); print(u.unquote(p.path))' " & quoted form of s
        end if
        
        try
            set a to POSIX file posixPath as alias
            tell application "Finder"
                set existing to tag names of a
                if existing does not contain "Red" then
                    set tag names of a to existing & {"Red"}
                end if
            end tell
        on error errMsg
            -- silently skip if path is invalid or inaccessible
            -- display dialog errMsg -- uncomment for debugging
        end try
    end if
end repeat
APPLESCRIPT