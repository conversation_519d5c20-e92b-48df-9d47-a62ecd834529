#!/usr/bin/env bash

# Script to copy file path from Pixea and add red Finder tag
# Usage: Run this script while <PERSON>xe<PERSON> is the frontmost application
# The script will send Command-C to copy the file path, then add a red tag
osascript <<'APPLESCRIPT'
-- Send Command-C to copy the path from Pixea
tell application "System Events" to keystroke "c" using command down
delay 0.3 -- give the clipboard a moment to update

set clipboardContent to the clipboard as text
if clipboardContent is "" then
    display notification "No file path found in clipboard" with title "Red Tag Script"
    return
end if

-- Process each line in case multiple paths are copied
set pathLines to paragraphs of clipboardContent
repeat with pathLine in pathLines
    set filePath to contents of pathLine
    if filePath is not "" then
        -- Convert file:// URL to POSIX path if necessary
        set posixPath to filePath
        if filePath starts with "file://" then
            try
                set posixPath to do shell script "python3 -c 'import sys,urllib.parse as u; p=u.urlsplit(sys.argv[1]); print(u.unquote(p.path))' " & quoted form of filePath
            on error
                -- If Python fails, try a simpler approach
                set posixPath to do shell script "echo " & quoted form of filePath & " | sed 's|^file://||' | sed 's|%20| |g'"
            end try
        end if

        -- Add red tag to the file
        try
            set fileAlias to POSIX file posixPath as alias
            tell application "Finder"
                set currentTags to tag names of fileAlias
                if currentTags does not contain "Red" then
                    set tag names of fileAlias to currentTags & {"Red"}
                    display notification "Red tag added to: " & (name of fileAlias) with title "Red Tag Script"
                else
                    display notification "Red tag already exists on: " & (name of fileAlias) with title "Red Tag Script"
                end if
            end tell
        on error errMsg
            display notification "Error tagging file: " & posixPath with title "Red Tag Script"
        end try
    end if
end repeat
APPLESCRIPT